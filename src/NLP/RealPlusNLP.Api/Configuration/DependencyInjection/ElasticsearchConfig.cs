using Microsoft.Extensions.Options;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class ElasticsearchConfig
{
    public static IServiceCollection AddElasticsearchConfig(
        this IServiceCollection services,
        ConfigurationManager configuration)
    {
        services.AddOptions<ElasticsearchOptions>()
            .Bind(configuration.GetSection(nameof(ElasticsearchOptions)))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        var esOptions = services.BuildServiceProvider().GetRequiredService<IOptions<ElasticsearchOptions>>().Value;

        

        services.AddSingleton(options =>
        {
            var esOptions = new ElasticsearchOptions();
            configuration.GetSection(nameof(ElasticsearchOptions)).Bind(esOptions);

            options.Nodes = new[] { new Uri(esOptions.Url) };
            options.BasicAuthentication = new BasicAuthentication(esOptions.Username, esOptions.Password);
        });        

        //services.AddSingleton<IElasticsearchService, ElasticsearchService>();

        return services;
    }
}
