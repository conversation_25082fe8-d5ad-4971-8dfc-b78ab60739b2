using System.Diagnostics.Metrics;
using System.Reflection;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class OpenTelemetryConfig
{
    public static IServiceCollection AddOpenTelemetryConfig(
        this IServiceCollection services,
        ILoggingBuilder loggingBuilder,
        IWebHostEnvironment environment,
        ConfigurationManager configuration)
    {
        var otlpOptions = new OpenTelemetryOptions();
        configuration.GetSection(nameof(OpenTelemetryOptions)).Bind(otlpOptions);

        var assemblyName = Assembly.GetExecutingAssembly().GetName().Name!;
        var otlpServiceName = otlpOptions.ServiceName;
        var otlpInstanceId =
            Environment.GetEnvironmentVariable("CONTAINER_APP_REVISION_ID") ??
            Environment.GetEnvironmentVariable("HOSTNAME") ??
            $"{Environment.MachineName}-{otlpServiceName}";

        var newrelicEndpoint = otlpOptions.NewrelicEndpoint;
        var newrelicApiKey = $"api-key={configuration["NEWRELIC_API_KEY"]}";

        loggingBuilder.ClearProviders();
        loggingBuilder.AddOpenTelemetry(logging =>
        {
            logging.IncludeFormattedMessage = true;
            logging.IncludeScopes = true;
            logging.ParseStateValues = true;
        });

        services
            .AddOpenTelemetry()
#if !DEBUG
            .UseAzureMonitor(cfg =>
            {
                cfg.ConnectionString = configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"];
            })
#endif
            .ConfigureResource(resource => resource
                .AddService(otlpServiceName)
                .AddAttributes(new Dictionary<string, object>
                {
                    ["deployment.environment"] = environment.EnvironmentName.ToLower(),
                    ["service.name"] = otlpServiceName,
                    ["service.instance.id"] = otlpInstanceId,
                    ["telemetry.sdk.language"] = "dotnet"
                }))
            .WithLogging(logging =>
            {
#if !DEBUG
                logging
                    .AddOtlpExporter(options =>
                    {
                        options.Endpoint = new Uri(newrelicEndpoint);
                        options.Headers = newrelicApiKey;
                        // options.BatchExportProcessorOptions = new BatchExportProcessorOptions<Activity>
                        // {
                        //     MaxQueueSize = 2048,
                        //     ScheduledDelayMilliseconds = 5000
                        // };
                    });
#endif
            })
            .WithTracing(tracing =>
            {
                if (environment.IsDevelopment())
                {
                    tracing.SetSampler<AlwaysOnSampler>();
                }
                tracing
                    .AddSource(
                        assemblyName,
                        $"{assemblyName}.AzureOpenAI",
                        "Azure.Core",
                        "Microsoft.AspNetCore.Hosting",
                        "Microsoft.AspNetCore.Server.Kestrel",
                        "System.Net.Http",
                        "Experimental.Microsoft.Extensions.AI")
                    .AddAspNetCoreInstrumentation(options =>
                    {
                        options.EnrichWithHttpRequest = (activity, request) =>
                        {
                            activity.SetTag("http.client_ip",
                                request.HttpContext.Connection.RemoteIpAddress);
                        };
                    })
                    .AddHttpClientInstrumentation()
#if !DEBUG
                    .AddOtlpExporter(options =>
                    {
                        options.Endpoint = new Uri(newrelicEndpoint);
                        options.Headers = newrelicApiKey;
                    })
#endif
                    ;
            })
            .WithMetrics(metric =>
            {
                metric
                    .AddMeter(
                        assemblyName,
                        $"{assemblyName}.AzureOpenAI",
                        "Azure.Core",
                        "Microsoft.AspNetCore.Hosting",
                        "Microsoft.AspNetCore.Server.Kestrel",
                        "System.Net.Http",
                        "Experimental.Microsoft.Extensions.AI")
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddView(config =>
                    {
                        return config.GetType().GetGenericTypeDefinition() == typeof(Histogram<>)
                            ? new Base2ExponentialBucketHistogramConfiguration() : null;
                    })
#if !DEBUG
                    .AddOtlpExporter(options =>
                    {
                        options.Endpoint = new Uri(newrelicEndpoint);
                        options.Headers = newrelicApiKey;
                    })
#endif
                    ;
            });

#if DEBUG
        // log all telemetry into the console
        // services.ConfigureOpenTelemetryLoggerProvider(l => l.AddConsoleExporter());
        // services.ConfigureOpenTelemetryTracerProvider(t => t.AddConsoleExporter());
        // services.ConfigureOpenTelemetryMeterProvider(m => m.AddConsoleExporter());
        // log all telemetry into the Aspire Dashboard
        services.ConfigureOpenTelemetryLoggerProvider(l => l.AddOtlpExporter());
        services.ConfigureOpenTelemetryTracerProvider(t => t.AddOtlpExporter());
        services.ConfigureOpenTelemetryMeterProvider(m => m.AddOtlpExporter());
#endif

        return services;
    }
}
