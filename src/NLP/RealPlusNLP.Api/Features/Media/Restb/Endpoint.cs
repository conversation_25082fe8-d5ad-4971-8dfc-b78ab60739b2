using Microsoft.AspNetCore.Mvc;

namespace RealPlusNLP.Api.Features.Media.Restb;

public sealed class Endpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("media/restb/callback", (
            [FromBody] object? request) => TypedResults.NoContent())
            .Produces(StatusCodes.Status204NoContent)
            .WithName("Callback")
            .WithTags("Media-Restb")
            .WithSummary("Callback endpoint for Restb API endpoint to manage an index")
            .WithDescription("Callback endpoint for Restb API endpoint to manage an index")
            .WithOpenApi();
    }
}
