using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class ElasticsearchService(IElasticClient client) : IElasticsearchService
{
    private readonly IElasticClient _client = client;

    public async Task<ISearchResponse<PropertyDocument>> SearchAsync(
        string query,
        CancellationToken cancellationToken = default)
    {
        var response = await _client.SearchAsync<PropertyDocument>(s => s
            .Query(q => q
                .QueryString(qs => qs
                    .Query(query)
                    .Fields(f => f
                        .Field(p => p.BuildingName)))
            .Size(1000)
            .TrackTotalHits(true)
            .TrackScores(true)
            .CancellationToken(cancellationToken));

        return response;
    }
}
